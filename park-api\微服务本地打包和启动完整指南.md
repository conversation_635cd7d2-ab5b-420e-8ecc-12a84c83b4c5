# 🚀 微服务本地打包和启动完整指南

## 📋 目录
1. [环境准备](#环境准备)
2. [微服务打包成Docker镜像](#微服务打包成docker镜像)
3. [基础服务启动](#基础服务启动)
4. [微服务启动](#微服务启动)
5. [完整启动流程](#完整启动流程)
6. [常用运维命令](#常用运维命令)

---

## 🔧 环境准备

### 必需软件
- **Java 8+**
- **Maven 3.6+**
- **Docker 20.0+**
- **Docker Compose 1.29+**

### 验证环境
```bash
# 验证Java版本
java -version

# 验证Maven版本
mvn -version

# 验证Docker版本
docker --version
docker-compose --version
```

---

## 📦 微服务打包成Docker镜像

### Maven Profile说明
项目提供了3个Maven Profile：

| Profile | 说明 | Docker构建 | Docker推送 |
|---------|------|------------|------------|
| **dev** (默认) | 开发环境 | ❌ 跳过 | ❌ 跳过 |
| **docker** | Docker构建 | ✅ 启用 | ❌ 跳过 |
| **prod** | 生产环境 | ✅ 启用 | ✅ 启用 |

### 1. 打包所有微服务镜像

#### 方法一：一键打包所有服务
```bash
# 进入项目根目录
cd /home/<USER>/park/park-api

# 清理并打包所有微服务（使用docker profile）
mvn clean package -Pdocker -DskipTests

# 构建所有Docker镜像
mvn docker:build -Pdocker
```

#### 方法二：逐个打包微服务

```bash
# 1. 网关服务
cd lgjy-gateway
mvn clean package -Pdocker -DskipTests
mvn docker:build -Pdocker
cd ..

# 2. 认证服务
cd lgjy-auth
mvn clean package -Pdocker -DskipTests
mvn docker:build -Pdocker
cd ..

# 3. 系统管理服务
cd lgjy-modules/lgjy-system
mvn clean package -Pdocker -DskipTests
mvn docker:build -Pdocker
cd ../..

# 4. 文件服务
cd lgjy-modules/lgjy-file
mvn clean package -Pdocker -DskipTests
mvn docker:build -Pdocker
cd ../..

# 5. 微信小程序服务
cd lgjy-modules/lgjy-wx
mvn clean package -Pdocker -DskipTests
mvn docker:build -Pdocker
cd ../..

# 6. 微信认证服务
cd lgjy-wx-auth
mvn clean package -Pdocker -DskipTests
mvn docker:build -Pdocker
cd ..

# 7. 道闸服务
cd lgjy-modules/lgjy-gate
mvn clean package -Pdocker -DskipTests
mvn docker:build -Pdocker
cd ../..
```

### 2. 验证镜像构建结果

```bash
# 查看构建的镜像
docker images | grep park

# 预期输出：
# park/lgjy-gateway          3.6.6    xxx    xxx    xxx MB
# park/lgjy-auth             3.6.6    xxx    xxx    xxx MB
# park/lgjy-modules-system   3.6.6    xxx    xxx    xxx MB
# park/lgjy-modules-file     3.6.6    xxx    xxx    xxx MB
# park/lgjy-modules-wx       3.6.6    xxx    xxx    xxx MB
# park/lgjy-wx-auth          3.6.6    xxx    xxx    xxx MB
# park/lgjy-modules-gate     3.6.6    xxx    xxx    xxx MB
```

---

## 🗄️ 基础服务启动

### 启动顺序：MySQL → Redis → Nacos

#### 1. 启动MySQL数据库
```bash
# 启动MySQL（第一优先级）
docker-compose up -d mysql

# 等待MySQL完全启动（约30-60秒）
docker-compose logs -f mysql

# 验证MySQL健康状态
docker-compose ps mysql
curl -f http://localhost:3306 || echo "MySQL is running"
```

#### 2. 启动Redis缓存
```bash
# 启动Redis（第二优先级）
docker-compose up -d redis

# 验证Redis状态
docker-compose logs redis
docker exec park-redis redis-cli -a qR6bW9kFzT3Zv ping
```

#### 3. 启动Nacos注册中心
```bash
# 启动Nacos（第三优先级，依赖MySQL）
docker-compose up -d nacos

# 等待Nacos启动完成（约60-120秒）
docker-compose logs -f nacos

# 验证Nacos Web界面
curl -f http://localhost:8848/nacos
# 或在浏览器访问：http://localhost:8848/nacos
# 用户名/密码：nacos/Tp9Vc2JxY6HdM
```

#### 4. 验证基础服务状态
```bash
# 检查所有基础服务状态
docker-compose ps mysql redis nacos

# 检查网络连通性
docker network ls | grep park
docker network inspect park-network
```

---

## 🚀 微服务启动

### 启动顺序：Gateway → Auth → Business Services

#### 1. 启动网关服务（核心服务）
```bash
# 启动网关（第一优先级）
docker-compose up -d park-gateway

# 查看启动日志
docker-compose logs -f park-gateway

# 验证网关健康状态
curl -s http://localhost:8080/actuator/health | jq .
```

#### 2. 启动认证服务
```bash
# 启动认证服务
docker-compose up -d park-auth

# 查看启动日志
docker-compose logs -f park-auth

# 验证认证服务
curl -s http://localhost:9204/actuator/health | jq .
```

#### 3. 启动业务微服务

```bash
# 启动系统管理服务
docker-compose up -d park-system
docker-compose logs -f park-system

# 启动文件服务
docker-compose up -d park-file
docker-compose logs -f park-file

# 启动微信认证服务
docker-compose up -d park-wx-auth
docker-compose logs -f park-wx-auth

# 启动微信小程序服务
docker-compose up -d park-wx
docker-compose logs -f park-wx

# 启动道闸服务
docker-compose up -d park-gate
docker-compose logs -f park-gate
```

#### 4. 验证所有微服务状态
```bash
# 查看所有服务状态
docker-compose ps

# 检查服务注册情况（Nacos控制台）
curl -s "http://localhost:8848/nacos/v1/ns/instance/list?serviceName=park-gateway"

# 验证网关路由
curl -s http://localhost:8080/actuator/gateway/routes | jq .
```

---

## 🔄 完整启动流程

### 一键启动脚本

创建启动脚本 `start-all.sh`：

```bash
#!/bin/bash

echo "🚀 开始启动停车场管理系统..."

# 1. 启动基础服务
echo "📊 启动基础服务..."
docker-compose up -d mysql redis nacos

# 2. 等待基础服务就绪
echo "⏳ 等待基础服务启动完成..."
sleep 60

# 3. 验证基础服务
echo "✅ 验证基础服务状态..."
docker-compose ps mysql redis nacos

# 4. 启动核心服务
echo "🌐 启动网关服务..."
docker-compose up -d park-gateway
sleep 30

# 5. 启动认证服务
echo "🔐 启动认证服务..."
docker-compose up -d park-auth
sleep 20

# 6. 启动业务服务
echo "💼 启动业务微服务..."
docker-compose up -d park-system park-file park-wx-auth park-wx park-gate

# 7. 等待所有服务启动
echo "⏳ 等待所有服务启动完成..."
sleep 60

# 8. 显示最终状态
echo "📋 系统启动完成，服务状态："
docker-compose ps

echo "🌐 访问地址："
echo "  - Nacos控制台: http://localhost:8848/nacos (nacos/Tp9Vc2JxY6HdM)"
echo "  - 网关地址: http://localhost:8080"
echo "  - 系统管理: http://localhost:8080/system"
```

### 使用启动脚本
```bash
# 给脚本执行权限
chmod +x start-all.sh

# 执行启动脚本
./start-all.sh
```

### 分步启动（推荐）

```bash
# 第一步：启动基础服务
docker-compose up -d mysql redis nacos
echo "等待基础服务启动..." && sleep 60

# 第二步：启动核心服务
docker-compose up -d park-gateway park-auth
echo "等待核心服务启动..." && sleep 30

# 第三步：启动业务服务
docker-compose up -d park-system park-file park-wx-auth park-wx park-gate
echo "等待业务服务启动..." && sleep 60

# 第四步：验证所有服务
docker-compose ps
```

---

## 🛠️ 常用运维命令

### 服务管理命令

```bash
# 查看所有服务状态
docker-compose ps

# 查看特定服务日志
docker-compose logs -f [service-name]
docker-compose logs -f park-gateway

# 重启特定服务
docker-compose restart [service-name]
docker-compose restart park-gateway

# 停止特定服务
docker-compose stop [service-name]
docker-compose stop park-gateway

# 停止所有服务
docker-compose down

# 停止并删除数据卷（危险操作）
docker-compose down -v
```

### 健康检查命令

```bash
# 检查网关健康状态
curl -s http://localhost:8080/actuator/health | jq .

# 检查认证服务健康状态
curl -s http://localhost:9204/actuator/health | jq .

# 检查系统管理服务健康状态
curl -s http://localhost:9201/actuator/health | jq .

# 检查所有服务的健康状态
for port in 8080 9201 9202 9203 9204 9205 9206; do
  echo "检查端口 $port:"
  curl -s http://localhost:$port/actuator/health | jq -r '.status // "UNKNOWN"'
done
```

### 资源监控命令

```bash
# 查看容器资源使用情况
docker stats

# 查看特定服务资源使用
docker stats park-gateway park-auth park-system

# 查看磁盘使用情况
docker system df

# 清理未使用的镜像和容器
docker system prune -f
```

### 故障排查命令

```bash
# 查看容器详细信息
docker inspect [container-name]

# 进入容器内部
docker exec -it [container-name] /bin/bash

# 查看容器启动命令
docker inspect [container-name] | jq '.[0].Config.Cmd'

# 查看容器环境变量
docker inspect [container-name] | jq '.[0].Config.Env'

# 查看网络连接
docker network inspect park-network
```

---

## 📊 服务端口映射表

| 服务名称 | 容器端口 | 宿主机端口 | 访问地址 |
|---------|----------|------------|----------|
| **MySQL** | 3306 | 3306 | localhost:3306 |
| **Redis** | 6379 | 6380 | localhost:6380 |
| **Nacos** | 8848 | 8848 | http://localhost:8848/nacos |
| **Gateway** | 8080 | 8080 | http://localhost:8080 |
| **System** | 9201 | 9201 | http://localhost:9201 |
| **File** | 9202 | 9202 | http://localhost:9202 |
| **Gate** | 9203 | 9203 | http://localhost:9203 |
| **Auth** | 9204 | 9204 | http://localhost:9204 |
| **WxAuth** | 9205 | 9205 | http://localhost:9205 |
| **Wx** | 9206 | 9206 | http://localhost:9206 |

---

## ⚠️ 注意事项

### 启动顺序很重要
1. **基础服务优先**：MySQL → Redis → Nacos
2. **核心服务其次**：Gateway → Auth
3. **业务服务最后**：System、File、Wx、WxAuth、Gate

### 常见问题解决

1. **服务启动失败**
   ```bash
   # 查看详细错误日志
   docker-compose logs [service-name]

   # 重新构建镜像
   mvn clean package -Pdocker -DskipTests
   mvn docker:build -Pdocker
   ```

2. **端口冲突**
   ```bash
   # 查看端口占用
   netstat -tulpn | grep [port]

   # 修改docker-compose.yml中的端口映射
   ```

3. **内存不足**
   ```bash
   # 查看系统资源
   free -h
   docker stats

   # 调整JVM参数（在docker-compose.yml中）
   JAVA_OPTS: "-Xms256m -Xmx512m"
   ```

### 性能优化建议

1. **调整JVM参数**：根据服务器内存调整各服务的内存分配
2. **使用SSD存储**：数据库和日志文件使用SSD存储
3. **监控资源使用**：定期检查CPU、内存、磁盘使用情况
4. **日志轮转**：配置日志文件大小限制和轮转策略

### 🎯 快速启动检查清单

- [ ] 确认Docker和Docker Compose已安装
- [ ] 确认所有微服务镜像已构建
- [ ] 按顺序启动基础服务（MySQL → Redis → Nacos）
- [ ] 等待基础服务完全启动（约2-3分钟）
- [ ] 启动核心服务（Gateway → Auth）
- [ ] 启动业务服务（System、File、Wx、WxAuth、Gate）
- [ ] 验证所有服务健康状态
- [ ] 检查Nacos控制台中的服务注册情况
- [ ] 测试网关路由是否正常工作

---

**📝 文档版本**: v1.0
**📅 更新时间**: 2025-07-25
**👨‍💻 维护者**: 停车场管理系统开发团队

---

这个完整的指南涵盖了从微服务打包到启动的所有步骤，按照这个流程操作可以确保系统正常启动和运行。如有问题，请参考故障排查部分或联系开发团队。
