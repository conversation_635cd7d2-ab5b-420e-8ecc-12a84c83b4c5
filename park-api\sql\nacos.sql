/*
 Navicat MySQL Data Transfer

 Source Server         : 远程测试数据库
 Source Server Type    : MySQL
 Source Server Version : 80035
 Source Host           : 127.0.0.1:3306
 Source Schema         : nacos

 Target Server Type    : MySQL
 Target Server Version : 80035
 File Encoding         : 65001

 Date: 27/07/2025 00:03:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for config_info
-- ----------------------------
DROP TABLE IF EXISTS `config_info`;
CREATE TABLE `config_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'group_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'app_name',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'configuration description',
  `c_use` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'configuration usage',
  `effect` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '配置生效的描述',
  `type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '配置的类型',
  `c_schema` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '配置的模式',
  `encrypted_data_key` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '密钥',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfo_datagrouptenant`(`data_id`, `group_id`, `tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 54 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'config_info' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_info
-- ----------------------------
INSERT INTO `config_info` VALUES (2, 'park-gateway-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', '2e16dbee5afdae0f95509c69c83b42b9', '2020-05-14 14:17:55', '2025-07-25 12:44:58', NULL, '0:0:0:0:0:0:0:1', '', '', '网关模块', 'null', 'null', 'yaml', '', '');
INSERT INTO `config_info` VALUES (3, 'park-auth-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n', '756a03974b815ff3febec9744c633cf4', '2020-11-20 00:00:00', '2025-07-12 11:35:10', NULL, '0:0:0:0:0:0:0:1', '', '', '认证中心', 'null', 'null', 'yaml', '', '');
INSERT INTO `config_info` VALUES (5, 'park-system-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml', '1efaabbb1af2a55d8484c9232efec24a', '2020-11-20 00:00:00', '2025-07-12 11:35:16', NULL, '0:0:0:0:0:0:0:1', '', '', '系统模块', 'null', 'null', 'yaml', '', '');
INSERT INTO `config_info` VALUES (8, 'park-file-dev.yml', 'DEFAULT_GROUP', '# 本地文件上传    \nfile:\n    domain: http://**************:9202\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', '0c541f89595bd67bc4824b36c24f527e', '2020-11-20 00:00:00', '2025-07-24 23:01:43', NULL, '0:0:0:0:0:0:0:1', '', '', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (10, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n', 'a85b912b55c71764159f7f1487c59c5a', '2025-06-07 11:34:11', '2025-07-12 11:35:30', NULL, '0:0:0:0:0:0:0:1', '', '', '微信小程序认证中心', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (11, 'park-wx-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://4924d67a2cbb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://4924d67a2cbb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://4924d67a2cbb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 15\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    # invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://4924d67a2cbb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', 'eca7ee68fbbbc47cd0d41d84e47fdc5d', '2025-06-07 18:11:24', '2025-07-26 10:23:39', NULL, '0:0:0:0:0:0:0:1', '', '', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (21, 'park-gate-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true', 'e5c4a07fc4ddfc06f70c489f3cda79bc', '2025-06-10 21:01:06', '2025-07-12 11:35:42', NULL, '0:0:0:0:0:0:0:1', '', '', 'lgjy-gate模块数据源配置', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (24, 'sizhuo-config.yml', 'DEFAULT_GROUP', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n', '84fa2dbf0322c86010606bdddf8580ac', '2025-07-11 10:32:41', '2025-07-12 11:35:45', NULL, '0:0:0:0:0:0:0:1', '', '', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (26, 'common-dev.yml', 'DEFAULT_GROUP', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n  \n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false', '2cf96926b52a27aec60b22f55fffdd55', '2025-07-12 09:52:04', '2025-07-12 22:04:22', NULL, '0:0:0:0:0:0:0:1', '', '', '通用配置文件-所有微服务共享', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (27, 'park-gateway-test.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统管理\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸控制\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 管理端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,routes,gateway\n  endpoint:\n    health:\n      show-details: always\n\n# 安全配置\nsecurity:\n  # 验证码设置\n  captcha:\n    enabled: true\n    type: math\n  # XSS防护\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /code\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut', 'aba808eb8e16fa4382d17a8c02e5b99b', '2025-07-26 12:56:41', '2025-07-26 12:56:41', '', '0:0:0:0:0:0:0:1', '', 'db222c85-606a-489b-95ac-ac568029b9b2', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (28, 'park-auth-test.yml', 'DEFAULT_GROUP', '# 数据源配置\nspring:\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      web-stat-filter:\n        enabled: true\n      stat-view-servlet:\n        enabled: true\n        allow:\n        url-pattern: /druid/*\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '36db0103978f68a849709484eac784cd', '2025-07-26 12:56:41', '2025-07-26 12:56:41', '', '0:0:0:0:0:0:0:1', '', 'db222c85-606a-489b-95ac-ac568029b9b2', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (29, 'park-system-test.yml', 'DEFAULT_GROUP', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        allow:\n        url-pattern: /druid/*\n        # 监控页面登录用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.system\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '3076468f95d4f8efebd042c7d3ba84cc', '2025-07-26 12:56:41', '2025-07-26 12:56:41', '', '0:0:0:0:0:0:0:1', '', 'db222c85-606a-489b-95ac-ac568029b9b2', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (30, 'park-file-test.yml', 'DEFAULT_GROUP', '# 本地文件上传配置 - Docker容器路径\nfile:\n    domain: http://127.0.0.1:9300\n    path: /app/upload\n    prefix: /statics\n\n# FastDFS分布式文件系统配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio对象存储配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      # 单个文件最大大小\n      max-file-size: 10MB\n      # 总请求最大大小\n      max-request-size: 20MB\n      # 启用文件上传\n      enabled: true\n      # 大文件延迟解析\n      resolve-lazily: false\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '5e8ec771f6899169751d53e972d0d047', '2025-07-26 12:56:41', '2025-07-26 12:56:41', '', '0:0:0:0:0:0:0:1', '', 'db222c85-606a-489b-95ac-ac568029b9b2', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (31, 'sizhuo-config-test.yml', 'DEFAULT_GROUP', '# 思卓品牌道闸配置\nsizhuo:\n  # 品牌标识\n  brandId: sizhuo\n  # 品牌名称\n  brandName: 思卓\n  # 道闸API请求地址\n  requestUrl: http://qr.it-wy.cn:83\n  # 停车场区域配置\n  areas:\n    - # 物理停车场ID\n      physicalId: 20240926133058945          \n      # 逻辑停车场ID\n      logicalId: 728424271419936768               \n      # 停车场名称\n      name: 江山路\n      # 通道规则配置\n      channelRules:\n        # 入场不收费通道列表（通道3免费入场）\n        entryDisabledChannels: [\"3\"]  \n        # 出场不收费通道列表（通道4免费出场）\n        exitDisabledChannels: [\"4\"]', 'bd6c04ae7612a8f600ca82944277d57a', '2025-07-26 12:56:41', '2025-07-26 12:56:41', '', '0:0:0:0:0:0:0:1', '', 'db222c85-606a-489b-95ac-ac568029b9b2', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (32, 'park-wx-test.yml', 'DEFAULT_GROUP', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.wx\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    # 开启驼峰命名转换\n    map-underscore-to-camel-case: true\n\n# 小程序API配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grant-type: authorization_code\n\n# 银联支付API配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  app-id: wxdcd31ee3e79190cc\n  app-key: 2e18a85cfa56e071fa00e96aaa9012cc\n  # 订单支付回调地址\n  notify-url-order: https://test-parking.example.com/wx/parking/order/payCallback\n  # VIP会员支付回调地址\n  notify-url-vip: https://test-parking.example.com/wx/package/payCallback\n  # 支付宝订单回调地址\n  notify-url-order-alipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\n  # 请求超时时间（秒）\n  timeout: 5\n  # 发票配置\n  invoice-url: https://fapiao.chinaums.com/fapiao-api/\n  invoice-msg-src: LINGANG_JT\n  invoice-key: 12cd2d10f2204107bf87df57aa7a4c4e\n  # 发票回调地址\n  notify-url-order-invoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', 'e9f0a12c4f85b5d72a05fb3da5e999c6', '2025-07-26 12:56:41', '2025-07-26 12:56:41', '', '0:0:0:0:0:0:0:1', '', 'db222c85-606a-489b-95ac-ac568029b9b2', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (33, 'park-wxauth-test.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n    \n# 短信服务配置\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: test-api-user\n  api-key: ************************************\n  sign: 【捷运停车充电】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 微信小程序API配置\nwx-api:\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '810183f69311ef7af4b226518de7d807', '2025-07-26 12:56:41', '2025-07-26 12:56:41', '', '0:0:0:0:0:0:0:1', '', 'db222c85-606a-489b-95ac-ac568029b9b2', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (34, 'park-gate-test.yml', 'DEFAULT_GROUP', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.gate\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    # 开启驼峰命名转换\n    map-underscore-to-camel-case: true\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '77af2fd1c3c7fe99666d2f926643b291', '2025-07-26 12:56:41', '2025-07-26 12:56:41', '', '0:0:0:0:0:0:0:1', '', 'db222c85-606a-489b-95ac-ac568029b9b2', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (35, 'common-test.yml', 'DEFAULT_GROUP', '# 测试环境通用配置\r\n# 所有微服务共享配置\r\n\r\n# 日志配置\r\nlogging:\r\n  level:\r\n    com.lgjy: INFO\r\n  file:\r\n    name: logs/${spring.application.name}/app.log\r\n    max-size: 100MB\r\n    max-history: 30\r\n\r\n# Sentinel配置\r\nspring:\r\n  cloud:\r\n    sentinel:\r\n      log:\r\n        dir: logs/sentinel\r\n        switch-pid: false\r\n\r\n# 管理端点配置\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: health,info,metrics\r\n  endpoint:\r\n    health:\r\n      show-details: always', 'f3a501aa32864dfc2fbe9ed2e203aff0', '2025-07-26 12:56:41', '2025-07-26 12:56:41', '', '0:0:0:0:0:0:0:1', '', 'db222c85-606a-489b-95ac-ac568029b9b2', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (45, 'park-gateway-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'dd356040fba2edd284003e0cce48f9f5', '2025-07-26 13:07:31', '2025-07-26 13:50:03', NULL, '0:0:0:0:0:0:0:1', '', '15456384-8b5a-416c-b72a-053ae7042cf9', '网关模块', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (46, 'park-auth-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n', 'ada66317db40469d330f0ad613fbfe5a', '2025-07-26 13:07:31', '2025-07-26 13:14:09', NULL, '0:0:0:0:0:0:0:1', '', '15456384-8b5a-416c-b72a-053ae7042cf9', '认证中心', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (47, 'park-system-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n        \n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml', '0c9321228d8128d97163d5f3c88c4980', '2025-07-26 13:07:31', '2025-07-26 13:21:00', NULL, '0:0:0:0:0:0:0:1', '', '15456384-8b5a-416c-b72a-053ae7042cf9', '系统模块', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (48, 'park-file-dev.yml', 'DEFAULT_GROUP', '# 本地文件上传    \nfile:\n    domain: http://**************:9202\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', '0c541f89595bd67bc4824b36c24f527e', '2025-07-26 13:07:31', '2025-07-26 13:07:31', NULL, '0:0:0:0:0:0:0:1', '', '15456384-8b5a-416c-b72a-053ae7042cf9', '', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (49, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n', 'f895d9853b1f67df41e2d9fc3fca5500', '2025-07-26 13:07:31', '2025-07-26 13:21:50', NULL, '0:0:0:0:0:0:0:1', '', '15456384-8b5a-416c-b72a-053ae7042cf9', '微信小程序认证中心', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (50, 'park-wx-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://4924d67a2cbb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://4924d67a2cbb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://4924d67a2cbb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 15\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    # invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://4924d67a2cbb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', 'd352675b40efb2ef5a6994d2e7aca7f0', '2025-07-26 13:07:31', '2025-07-26 13:22:23', NULL, '0:0:0:0:0:0:0:1', '', '15456384-8b5a-416c-b72a-053ae7042cf9', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (51, 'park-gate-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true', '80acab5e032b58965b9a34e7052b1783', '2025-07-26 13:07:31', '2025-07-26 13:22:54', NULL, '0:0:0:0:0:0:0:1', '', '15456384-8b5a-416c-b72a-053ae7042cf9', 'lgjy-gate模块数据源配置', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (52, 'sizhuo-config.yml', 'DEFAULT_GROUP', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n', '84fa2dbf0322c86010606bdddf8580ac', '2025-07-26 13:07:31', '2025-07-26 13:07:31', NULL, '0:0:0:0:0:0:0:1', '', '15456384-8b5a-416c-b72a-053ae7042cf9', '', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (53, 'common-dev.yml', 'DEFAULT_GROUP', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n  \n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false', '2cf96926b52a27aec60b22f55fffdd55', '2025-07-26 13:07:31', '2025-07-26 13:07:31', NULL, '0:0:0:0:0:0:0:1', '', '15456384-8b5a-416c-b72a-053ae7042cf9', '通用配置文件-所有微服务共享', NULL, NULL, 'yaml', NULL, '');

-- ----------------------------
-- Table structure for config_info_aggr
-- ----------------------------
DROP TABLE IF EXISTS `config_info_aggr`;
CREATE TABLE `config_info_aggr`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'datum_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '内容',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfoaggr_datagrouptenantdatum`(`data_id`, `group_id`, `tenant_id`, `datum_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '增加租户字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_info_aggr
-- ----------------------------

-- ----------------------------
-- Table structure for config_info_beta
-- ----------------------------
DROP TABLE IF EXISTS `config_info_beta`;
CREATE TABLE `config_info_beta`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '密钥',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfobeta_datagrouptenant`(`data_id`, `group_id`, `tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'config_info_beta' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_info_beta
-- ----------------------------

-- ----------------------------
-- Table structure for config_info_gray
-- ----------------------------
DROP TABLE IF EXISTS `config_info_gray`;
CREATE TABLE `config_info_gray`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'group_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'md5',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT 'src_user',
  `src_ip` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'src_ip',
  `gmt_create` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT 'gmt_create',
  `gmt_modified` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT 'gmt_modified',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'app_name',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT 'tenant_id',
  `gray_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'gray_name',
  `gray_rule` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'gray_rule',
  `encrypted_data_key` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT 'encrypted_data_key',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfogray_datagrouptenantgray`(`data_id`, `group_id`, `tenant_id`, `gray_name`) USING BTREE,
  INDEX `idx_dataid_gmt_modified`(`data_id`, `gmt_modified`) USING BTREE,
  INDEX `idx_gmt_modified`(`gmt_modified`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'config_info_gray' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_info_gray
-- ----------------------------

-- ----------------------------
-- Table structure for config_info_tag
-- ----------------------------
DROP TABLE IF EXISTS `config_info_tag`;
CREATE TABLE `config_info_tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tag_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'source ip',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfotag_datagrouptenanttag`(`data_id`, `group_id`, `tenant_id`, `tag_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'config_info_tag' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_info_tag
-- ----------------------------

-- ----------------------------
-- Table structure for config_tags_relation
-- ----------------------------
DROP TABLE IF EXISTS `config_tags_relation`;
CREATE TABLE `config_tags_relation`  (
  `id` bigint NOT NULL COMMENT 'id',
  `tag_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_name',
  `tag_type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'tag_type',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `nid` bigint NOT NULL AUTO_INCREMENT COMMENT 'nid, 自增长标识',
  PRIMARY KEY (`nid`) USING BTREE,
  UNIQUE INDEX `uk_configtagrelation_configidtag`(`id`, `tag_name`, `tag_type`) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'config_tag_relation' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_tags_relation
-- ----------------------------

-- ----------------------------
-- Table structure for group_capacity
-- ----------------------------
DROP TABLE IF EXISTS `group_capacity`;
CREATE TABLE `group_capacity`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Group ID，空字符表示整个集群',
  `quota` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数，，0表示使用默认值',
  `max_aggr_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '集群、各Group容量信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of group_capacity
-- ----------------------------

-- ----------------------------
-- Table structure for his_config_info
-- ----------------------------
DROP TABLE IF EXISTS `his_config_info`;
CREATE TABLE `his_config_info`  (
  `id` bigint UNSIGNED NOT NULL COMMENT 'id',
  `nid` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'nid, 自增标识',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'source ip',
  `op_type` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'operation type',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '密钥',
  `publish_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT 'formal' COMMENT 'publish type gray or formal',
  `gray_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'gray name',
  `ext_info` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT 'ext info',
  PRIMARY KEY (`nid`) USING BTREE,
  INDEX `idx_gmt_create`(`gmt_create`) USING BTREE,
  INDEX `idx_gmt_modified`(`gmt_modified`) USING BTREE,
  INDEX `idx_did`(`data_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 250 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '多租户改造' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of his_config_info
-- ----------------------------
INSERT INTO `his_config_info` VALUES (11, 88, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\nsnowflake:\n  worker-id: 2      # 取值范围：0~31\n  datacenter-id: 2   # 取值范围：0~31\n\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/payCallback\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', 'e7d72822e64d76bd2e3404b6cea1550e', '2025-06-27 09:55:37', '2025-06-27 01:55:38', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (21, 89, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 723055983177371648\n      name: 1\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]        ', '69540a6ade454204d50c069095411885', '2025-07-02 12:40:24', '2025-07-02 04:40:25', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (21, 90, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 723055983177371648\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]        ', '21c8ca827416f0cfb55c9a1134c1d650', '2025-07-02 13:16:38', '2025-07-02 05:16:38', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 91, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\nsnowflake:\n  worker-id: 2      # 取值范围：0~31\n  datacenter-id: 2   # 取值范围：0~31\n\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlOrderAlipay: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '5865584f6cb63780cfd5d28c525725fb', '2025-07-04 10:01:40', '2025-07-04 02:01:40', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 92, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: park-gen\n          uri: lb://park-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/paycallback\n      - /wx/parking/order/channelPayQuery\n      - /gate/sizhuo/pull\n      - /wx/warehouse/list\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', 'ec3ccec0315a54662121dd7c624fcb1a', '2025-07-04 10:02:26', '2025-07-04 02:02:27', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 93, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\nsnowflake:\n  worker-id: 2      # 取值范围：0~31\n  datacenter-id: 2   # 取值范围：0~31\n\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://6c39-116-226-72-227.ngrok-free.app/wx/package/order/payCallback\n    notifyUrlOrderAlipay: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', 'c17ece7b9740d45bab8db451a8bc5884', '2025-07-05 15:27:15', '2025-07-05 07:27:16', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (21, 94, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 723055983177371648\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]        ', '21c8ca827416f0cfb55c9a1134c1d650', '2025-07-05 16:48:08', '2025-07-05 08:48:09', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 95, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\nsnowflake:\n  worker-id: 2      # 取值范围：0~31\n  datacenter-id: 2   # 取值范围：0~31\n\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://6758-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://6758-39-173-159-200.ngrok-free.app/wx/package/order/payCallback\n    notifyUrlOrderAlipay: https://6758-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '5f17fb2ad5bac034fe722d10136ffc12', '2025-07-06 09:30:42', '2025-07-06 01:30:42', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 96, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: park-gen\n          uri: lb://park-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /gate/sizhuo/pull\n      - /wx/warehouse/list\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n      - /wx/package/order/payCallback\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', '56d967e3fc47c901dbada7a8dbc1c619', '2025-07-06 09:49:00', '2025-07-06 01:49:00', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 97, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://875c-39-173-159-200.ngrok-free.app/wx/package/order/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '56710eaf69b0e5d0efca30a72dadd263', '2025-07-06 09:55:55', '2025-07-06 01:55:56', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 98, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://875c-39-173-159-200.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '76c9a6b73cc7cdfb77199d1238d7a266', '2025-07-06 15:18:56', '2025-07-06 07:18:57', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (10, 99, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxf5aea578b4a18ffb\n  secret: e3a6258c6e4beed78226e603bcb9c2da\n  grantType: authorization_code\n', 'd61c247c1eda80edbb90b6ee563ca20d', '2025-07-06 15:25:17', '2025-07-06 07:25:17', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (10, 100, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxf5a8941d0d5617c1\n  secret: 0ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n', '17273a3ac50b8d741e105964b82fdd6a', '2025-07-07 10:09:43', '2025-07-07 02:09:44', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 101, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: xf5a8941d0d5617c1\n    secret: 0ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://875c-39-173-159-200.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '8d78ca766247979acb7f22bf1eaa0a4c', '2025-07-08 10:21:30', '2025-07-08 02:21:30', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 102, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://875c-39-173-159-200.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '76c9a6b73cc7cdfb77199d1238d7a266', '2025-07-08 10:57:30', '2025-07-08 02:57:30', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 103, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://01a2c6b0249d.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', 'c91d58185273519e0cfcf2dbd20f3f7d', '2025-07-08 11:15:58', '2025-07-08 03:15:58', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (6, 104, 'park-gen-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: *****************************************************************************************************************************************************    username: root\n    password: 1234\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: zzz\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: zzz\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.lgjy.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n  # 是否允许生成文件覆盖到本地（自定义路径），默认不允许\n  allowOverwrite: false', '185f676be26dd2ddcf190d4240b0b10e', '2025-07-10 11:46:35', '2025-07-10 03:46:36', NULL, '0:0:0:0:0:0:0:1', 'D', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 105, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: park-gen\n          uri: lb://park-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /gate/sizhuo/pull\n      - /wx/warehouse/list\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n      - /wx/package/payCallback\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', '098cff6611f8b436ac22e499b6710813', '2025-07-10 11:56:53', '2025-07-10 03:56:54', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (23, 106, 'brand-mapping.yml', 'DEFAULT_GROUP', '', 'brand:\n  # 品牌映射配置：停车场ID -> 品牌编码\n  mapping:\n    # 思卓品牌停车场\n    \"728424271419936768\": \"sizhuo\"\n    # 未来可以添加其他品牌停车场\n    # \"另一个停车场ID\": \"jieshun\"\n    # \"第三个停车场ID\": \"brand3\"\n  \n  # 品牌服务配置：品牌编码 -> 服务地址\n  services:\n    sizhuo: \"http://localhost:9201\"\n    # 未来可以添加其他品牌服务\n    # jieshun: \"http://localhost:9202\"\n    # brand3: \"http://localhost:9203\"', 'deaead81051bd650bab1e78ea75c7eac', '2025-07-10 17:47:54', '2025-07-10 09:47:54', NULL, '0:0:0:0:0:0:0:1', 'D', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (22, 107, 'sizhuo-brand-config.yml', 'DEFAULT_GROUP', '', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 728424271419936768\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]\n    # 可以在这里添加更多思卓品牌的停车场\n    # - physicalId: 另一个物理ID\n    #   logicalId: 另一个逻辑ID\n    #   name: 另一个停车场名称\n    #   channelRules:\n    #     entryDisabledChannels: []\n    #     exitDisabledChannels: []', 'a8f327aac2995b64244e283787249719', '2025-07-10 17:48:02', '2025-07-10 09:48:03', NULL, '0:0:0:0:0:0:0:1', 'D', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (21, 108, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug', 'dd58f58e5217964178b23a4c24531898', '2025-07-10 17:48:13', '2025-07-10 09:48:13', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 109, 'sizhuo-config.yml', 'DEFAULT_GROUP', '', 'sizhuo:\r\n  brandId: sizhuo\r\n  brandName: 思卓\r\n  requestUrl: http://qr.it-wy.cn:83\r\n  # 停车场区域配置\r\n  areas:\r\n    - physicalId: \"SZ001\"          \r\n      logicalId: \"1\"               \r\n      name: \"江山路\"\r\n      channelRules:\r\n        entryDisabledChannels: []  \r\n        exitDisabledChannels: []   \r\n', '723dc2083b2a6d930e013809fd41e4b9', '2025-07-11 10:32:40', '2025-07-11 02:32:41', NULL, '0:0:0:0:0:0:0:1', 'I', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (24, 110, 'sizhuo-config.yml', 'DEFAULT_GROUP', '', 'sizhuo:\r\n  brandId: sizhuo\r\n  brandName: 思卓\r\n  requestUrl: http://qr.it-wy.cn:83\r\n  # 停车场区域配置\r\n  areas:\r\n    - physicalId: \"SZ001\"          \r\n      logicalId: \"1\"               \r\n      name: \"江山路\"\r\n      channelRules:\r\n        entryDisabledChannels: []  \r\n        exitDisabledChannels: []   \r\n', '723dc2083b2a6d930e013809fd41e4b9', '2025-07-11 10:34:07', '2025-07-11 02:34:08', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 111, 'Igjy-payment-dev.yml', 'DEFAULT_GROUP', '', '# 支付服务配置文件 (开发环境)\r\n# 支付服务负责统一的支付、退款、查询等功能\r\n\r\nspring:\r\n  # 数据源配置\r\n  datasource:\r\n    # Druid数据库连接池配置\r\n    druid:\r\n      # Druid监控页面配置\r\n      stat-view-servlet:\r\n        # 启用Druid监控页面\r\n        # 可通过 http://ip:port/druid 访问监控界面\r\n        enabled: true\r\n        # 监控页面登录用户名\r\n        loginUsername: lgjy\r\n        # 监控页面登录密码\r\n        loginPassword: 123456\r\n    \r\n    # 动态数据源配置 - 支持主从读写分离\r\n    dynamic:\r\n      # Druid连接池全局配置\r\n      druid:\r\n        # 初始化连接数：启动时创建5个连接\r\n        initial-size: 5\r\n        # 最小空闲连接数：连接池中保持的最少连接数\r\n        min-idle: 5\r\n        # 最大活跃连接数：连接池最多同时使用20个连接\r\n        maxActive: 20\r\n        # 获取连接最大等待时间：60秒\r\n        maxWait: 60000\r\n        # 连接超时时间：30秒\r\n        connectTimeout: 30000\r\n        # Socket读取超时时间：60秒\r\n        socketTimeout: 60000\r\n        # 连接回收检测间隔：60秒检测一次空闲连接\r\n        timeBetweenEvictionRunsMillis: 60000\r\n        # 连接最小空闲时间：5分钟未使用的连接将被回收\r\n        minEvictableIdleTimeMillis: 300000\r\n        # 连接有效性检测SQL语句\r\n        validationQuery: SELECT 1 FROM DUAL\r\n        # 空闲时检测连接有效性\r\n        testWhileIdle: true\r\n        # 获取连接时不检测（提高性能）\r\n        testOnBorrow: false\r\n        # 归还连接时不检测（提高性能）\r\n        testOnReturn: false\r\n        # 启用预编译语句池\r\n        poolPreparedStatements: true\r\n        # 每个连接最多缓存20个预编译语句\r\n        maxPoolPreparedStatementPerConnectionSize: 20\r\n        # 启用统计和日志过滤器\r\n        filters: stat,slf4j\r\n        # 连接属性：启用SQL合并统计，慢SQL阈值5秒\r\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\r\n      \r\n      # 数据源配置\r\n      datasource:\r\n          # 主数据库配置 - 用于写操作和主要读操作\r\n          master:\r\n            # MySQL 8.0+ 驱动\r\n            driver-class-name: com.mysql.cj.jdbc.Driver\r\n            # 数据库连接URL\r\n            url: **********************************w?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\r\n            # 数据库用户名\r\n            username: root\r\n            # 数据库密码\r\n            password: 1234\r\n\r\n# MyBatis ORM框架配置\r\nmybatis:\r\n    # 实体类包扫描路径\r\n    typeAliasesPackage: com.lgjy.payment.domain\r\n    # Mapper XML文件扫描路径\r\n    mapperLocations: classpath:mapper/**/*.xml\r\n\r\n# 银联支付配置\r\nunion-pay-api:\r\n  sign: 37Y1\r\n  url: https://api-mop.chinaums.com\r\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\r\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\r\n  timeout: 30\r\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\r\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\r\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\r\n  invoiceMsgSrc: lgjy_payment\r\n  invoiceKey: payment_invoice_key_2025\r\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\r\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\r\n\r\n# 微信小程序配置\r\napplet-api:\r\n  url: https://api.weixin.qq.com\r\n  appid: wxf5a8941d0d5617c1\r\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\r\n  grantType: authorization_code\r\n  timeout: 30\r\n  payUrl: https://api.mch.weixin.qq.com\r\n  mchIdOrder: 1659104869\r\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\r\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  mchIdVip: 1659104869\r\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\r\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  supportFapiao: false\r\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund', 'f7ce0035e606045398e15e690bdb2d5f', '2025-07-11 11:05:32', '2025-07-11 03:05:32', NULL, '0:0:0:0:0:0:0:1', 'I', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (25, 112, 'Igjy-payment-dev.yml', 'DEFAULT_GROUP', '', '# 支付服务配置文件 (开发环境)\r\n# 支付服务负责统一的支付、退款、查询等功能\r\n\r\nspring:\r\n  # 数据源配置\r\n  datasource:\r\n    # Druid数据库连接池配置\r\n    druid:\r\n      # Druid监控页面配置\r\n      stat-view-servlet:\r\n        # 启用Druid监控页面\r\n        # 可通过 http://ip:port/druid 访问监控界面\r\n        enabled: true\r\n        # 监控页面登录用户名\r\n        loginUsername: lgjy\r\n        # 监控页面登录密码\r\n        loginPassword: 123456\r\n    \r\n    # 动态数据源配置 - 支持主从读写分离\r\n    dynamic:\r\n      # Druid连接池全局配置\r\n      druid:\r\n        # 初始化连接数：启动时创建5个连接\r\n        initial-size: 5\r\n        # 最小空闲连接数：连接池中保持的最少连接数\r\n        min-idle: 5\r\n        # 最大活跃连接数：连接池最多同时使用20个连接\r\n        maxActive: 20\r\n        # 获取连接最大等待时间：60秒\r\n        maxWait: 60000\r\n        # 连接超时时间：30秒\r\n        connectTimeout: 30000\r\n        # Socket读取超时时间：60秒\r\n        socketTimeout: 60000\r\n        # 连接回收检测间隔：60秒检测一次空闲连接\r\n        timeBetweenEvictionRunsMillis: 60000\r\n        # 连接最小空闲时间：5分钟未使用的连接将被回收\r\n        minEvictableIdleTimeMillis: 300000\r\n        # 连接有效性检测SQL语句\r\n        validationQuery: SELECT 1 FROM DUAL\r\n        # 空闲时检测连接有效性\r\n        testWhileIdle: true\r\n        # 获取连接时不检测（提高性能）\r\n        testOnBorrow: false\r\n        # 归还连接时不检测（提高性能）\r\n        testOnReturn: false\r\n        # 启用预编译语句池\r\n        poolPreparedStatements: true\r\n        # 每个连接最多缓存20个预编译语句\r\n        maxPoolPreparedStatementPerConnectionSize: 20\r\n        # 启用统计和日志过滤器\r\n        filters: stat,slf4j\r\n        # 连接属性：启用SQL合并统计，慢SQL阈值5秒\r\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\r\n      \r\n      # 数据源配置\r\n      datasource:\r\n          # 主数据库配置 - 用于写操作和主要读操作\r\n          master:\r\n            # MySQL 8.0+ 驱动\r\n            driver-class-name: com.mysql.cj.jdbc.Driver\r\n            # 数据库连接URL\r\n            url: **********************************w?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\r\n            # 数据库用户名\r\n            username: root\r\n            # 数据库密码\r\n            password: 1234\r\n\r\n# MyBatis ORM框架配置\r\nmybatis:\r\n    # 实体类包扫描路径\r\n    typeAliasesPackage: com.lgjy.payment.domain\r\n    # Mapper XML文件扫描路径\r\n    mapperLocations: classpath:mapper/**/*.xml\r\n\r\n# 银联支付配置\r\nunion-pay-api:\r\n  sign: 37Y1\r\n  url: https://api-mop.chinaums.com\r\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\r\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\r\n  timeout: 30\r\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\r\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\r\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\r\n  invoiceMsgSrc: lgjy_payment\r\n  invoiceKey: payment_invoice_key_2025\r\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\r\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\r\n\r\n# 微信小程序配置\r\napplet-api:\r\n  url: https://api.weixin.qq.com\r\n  appid: wxf5a8941d0d5617c1\r\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\r\n  grantType: authorization_code\r\n  timeout: 30\r\n  payUrl: https://api.mch.weixin.qq.com\r\n  mchIdOrder: 1659104869\r\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\r\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  mchIdVip: 1659104869\r\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\r\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  supportFapiao: false\r\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund', 'f7ce0035e606045398e15e690bdb2d5f', '2025-07-11 11:11:09', '2025-07-11 03:11:09', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (25, 113, 'Igjy-payment-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# MyBatis ORM框架配置\nmybatis:\n    # 实体类包扫描路径\n    typeAliasesPackage: com.lgjy.payment.domain\n    # Mapper XML文件扫描路径\n    mapperLocations: classpath:mapper/**/*.xml\n\n# 银联支付配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\n  timeout: 30\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\n  invoiceMsgSrc: lgjy_payment\n  invoiceKey: payment_invoice_key_2025\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n\n# 微信小程序配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n  timeout: 30\n  payUrl: https://api.mch.weixin.qq.com\n  mchIdOrder: 1659104869\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  mchIdVip: 1659104869\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\n  supportFapiao: false\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund', '2b5b492912ba21b11a6b657e7466d9c8', '2025-07-11 11:12:24', '2025-07-11 03:12:25', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (25, 114, 'Igjy-payment-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# MyBatis ORM框架配置\nmybatis:\n    # 实体类包扫描路径\n    typeAliasesPackage: com.lgjy.payment.domain\n    # Mapper XML文件扫描路径\n    mapperLocations: classpath:mapper/**/*.xml\n\n# 银联支付配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\n  timeout: 30\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\n  invoiceMsgSrc: lgjy_payment\n  invoiceKey: payment_invoice_key_2025\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n\n# 微信小程序配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n  timeout: 30\n  payUrl: https://api.mch.weixin.qq.com\n  mchIdOrder: 1659104869\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  mchIdVip: 1659104869\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\n  supportFapiao: false\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund', '2b5b492912ba21b11a6b657e7466d9c8', '2025-07-11 11:25:40', '2025-07-11 03:25:40', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (25, 115, 'park-payment-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \"\"\n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: \"**********************************w?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\"\n            username: root\n            password: \"1234\"\n          # 从库数据源\n#         slave:\n#           username: \"\"\n#           password: \"\"\n#           url: \"\"\n#           driver-class-name: \"\"\n\n# MyBatis ORM框架配置\nmybatis:\n    # 实体类包扫描路径\n    typeAliasesPackage: com.lgjy.payment.domain\n    # Mapper XML文件扫描路径\n    mapperLocations: classpath:mapper/**/*.xml\n\n# 银联支付配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\n  timeout: 30\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\n  invoiceMsgSrc: lgjy_payment\n  invoiceKey: payment_invoice_key_2025\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n\n# 微信小程序配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n  timeout: 30\n  payUrl: https://api.mch.weixin.qq.com\n  mchIdOrder: 1659104869\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  mchIdVip: 1659104869\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\n  supportFapiao: false\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund', '4ffedc0226aaac9f1ced4c1d7f29de32', '2025-07-11 11:50:15', '2025-07-11 03:50:15', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (25, 116, 'park-payment-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \"\"\n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: \"**********************************w?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\"\n            username: root\n            password: \"1234\"\n          # 从库数据源\n#         slave:\n#           username: \"\"\n#           password: \"\"\n#           url: \"\"\n#           driver-class-name: \"\"\n\n# MyBatis ORM框架配置\nmybatis:\n    # 实体类包扫描路径\n    typeAliasesPackage: com.lgjy.payment.domain\n    # Mapper XML文件扫描路径\n    mapperLocations: classpath:mapper/**/*.xml\n\n# 银联支付配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\n  timeout: 30\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\n  invoiceMsgSrc: lgjy_payment\n  invoiceKey: payment_invoice_key_2025\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n\n# 微信小程序配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n  timeout: 30\n  payUrl: https://api.mch.weixin.qq.com\n  mchIdOrder: 1659104869\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  mchIdVip: 1659104869\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\n  supportFapiao: false\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund', '4ffedc0226aaac9f1ced4c1d7f29de32', '2025-07-11 12:34:22', '2025-07-11 04:34:22', NULL, '0:0:0:0:0:0:0:1', 'D', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 117, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://01a2c6b0249d.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '1c59bc9fa36e4687a901f465819b010b', '2025-07-11 12:45:31', '2025-07-11 04:45:32', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 118, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://01a2c6b0249d.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '8d71caf49615cd340bafd4d8fa7056de', '2025-07-11 12:48:00', '2025-07-11 04:48:00', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 119, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '62ac86adf1d66c6e4e66f648a95c74f3', '2025-07-11 12:55:00', '2025-07-11 04:55:01', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 120, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', 'c9c71b2fde5689d30ec70ca4726abad6', '2025-07-11 13:37:54', '2025-07-11 05:37:55', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 121, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', 'c0ae425785887e3d55e6db0fe282722c', '2025-07-11 13:38:23', '2025-07-11 05:38:23', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 122, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: 8a81c1bd89b6cadb018e08cf6b681acb\n    secret: ba89d775d4c04a5090e10abd927d2dd0\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '913c7edc90b26f60700c7948c33c9182', '2025-07-11 13:41:10', '2025-07-11 05:41:10', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 123, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '8f704b6a83cd4b039c72308ab915d32e', '2025-07-11 13:44:17', '2025-07-11 05:44:18', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 124, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '6d3d590f1486bc3bd1f9a3d1b59ba06b', '2025-07-11 13:46:42', '2025-07-11 05:46:42', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (10, 125, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n', '36b71596458ad7be7a45c2c431420800', '2025-07-11 13:48:36', '2025-07-11 05:48:36', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (1, 126, 'application-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true', 'b20a6949af246cfacbc14191e0377ea0', '2025-07-11 15:35:26', '2025-07-11 07:35:27', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (24, 127, 'sizhuo-config.yml', 'DEFAULT_GROUP', '', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  # 停车场区域配置\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n', '8ba1ca8e463547a0590dcc193fe27c91', '2025-07-11 15:41:13', '2025-07-11 07:41:14', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 128, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /gate/sizhuo/pull\n      - /wx/warehouse/list\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n      - /wx/package/payCallback', '4e6f196da6900e54ae745e86ef2e05d9', '2025-07-11 15:43:28', '2025-07-11 07:43:28', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 129, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', '9d9ff4e99df68d08462a4d9596dc96ae', '2025-07-11 15:44:35', '2025-07-11 07:44:35', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (5, 130, 'park-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '3424f8998e6fde133f8be1d88867e706', '2025-07-11 15:53:28', '2025-07-11 07:53:29', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 131, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '6d3d590f1486bc3bd1f9a3d1b59ba06b', '2025-07-11 15:55:26', '2025-07-11 07:55:27', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (5, 132, 'park-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.system\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '1c9dd8e39372d9ee1273ee4df8e9b870', '2025-07-11 15:56:13', '2025-07-11 07:56:13', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 133, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '855d89040aff64b0db48d686416163d5', '2025-07-11 15:56:41', '2025-07-11 07:56:41', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (5, 134, 'park-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.system\n    mapperLocations: classpath:mapper/**/*.xml', '9ffa7e2cba10c9f2326301c773922ece', '2025-07-11 15:57:52', '2025-07-11 07:57:52', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (21, 135, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 728424271419936768\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]', 'e21eef30bb36e97c596046a3e47c50f5', '2025-07-11 16:02:32', '2025-07-11 08:02:33', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 136, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234 \n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', '776594d4818f598cf6922e5ca9283d78', '2025-07-11 16:05:33', '2025-07-11 08:05:34', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (10, 137, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n', 'a85b912b55c71764159f7f1487c59c5a', '2025-07-11 16:08:17', '2025-07-11 08:08:18', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 138, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234 \n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', 'c77037ccd806f29ef37d4dc3d72fe709', '2025-07-11 16:11:38', '2025-07-11 08:11:39', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 139, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234 \n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n', 'e66774871b3eb0fb1b50fdd023925d89', '2025-07-11 16:11:53', '2025-07-11 08:11:53', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (21, 140, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 728424271419936768\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]', '02f7d3ba2420f7e017b3f73591ae0c3d', '2025-07-11 16:13:19', '2025-07-11 08:13:19', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (1, 141, 'application-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true', 'b20a6949af246cfacbc14191e0377ea0', '2025-07-11 16:22:29', '2025-07-11 08:22:29', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 142, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'e6f0cead56e94162e99231d0c3d18d79', '2025-07-12 08:00:08', '2025-07-12 00:00:09', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (3, 143, 'park-auth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n', '756a03974b815ff3febec9744c633cf4', '2025-07-12 08:00:14', '2025-07-12 00:00:14', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (5, 144, 'park-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml', '44d73acde87f75500940a819e0188906', '2025-07-12 08:00:19', '2025-07-12 00:00:19', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (8, 145, 'park-file-dev.yml', 'DEFAULT_GROUP', '', '# 本地文件上传    \nfile:\n    domain: http://127.0.0.1:9300\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', '95bb71a3e1301faa5d04c1e6685936cb', '2025-07-12 08:00:23', '2025-07-12 00:00:24', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (10, 146, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n', 'a85b912b55c71764159f7f1487c59c5a', '2025-07-12 08:00:29', '2025-07-12 00:00:29', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 147, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '3c5ed4b23aedeea41aec7d2162e34ffa', '2025-07-12 08:00:33', '2025-07-12 00:00:33', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 148, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '3c5ed4b23aedeea41aec7d2162e34ffa', '2025-07-12 08:00:37', '2025-07-12 00:00:37', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (21, 149, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug', '78c1484713099b5d9892bda7f1315e32', '2025-07-12 08:00:41', '2025-07-12 00:00:41', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (24, 150, 'sizhuo-config.yml', 'DEFAULT_GROUP', '', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n', '84fa2dbf0322c86010606bdddf8580ac', '2025-07-12 08:00:45', '2025-07-12 00:00:45', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (21, 151, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug', 'b8d5ad505f58a7be19f62311e1ef4573', '2025-07-12 08:04:51', '2025-07-12 00:04:51', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 152, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', 'ce47cb40aef5c3ae75f81706b75f06d2', '2025-07-12 08:08:55', '2025-07-12 00:08:56', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (5, 153, 'park-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml', '1efaabbb1af2a55d8484c9232efec24a', '2025-07-12 08:09:02', '2025-07-12 00:09:03', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (26, 154, 'common-dev.yml', 'DEFAULT_GROUP', '', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# 将来可能的通用配置预留位置', '305a7a3474bb1c67df959796b351ceab', '2025-07-12 10:29:10', '2025-07-12 02:29:11', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (26, 155, 'common-dev.yml', 'DEFAULT_GROUP', '', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false\n      transport:\n        port: 8719\n        dashboard: localhost:8080\n\n# 将来可能的通用配置预留位置', '4671bb9540b7c151fc3b21042a7028c8', '2025-07-12 10:38:39', '2025-07-12 02:38:39', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 156, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'e6f0cead56e94162e99231d0c3d18d79', '2025-07-12 11:35:06', '2025-07-12 03:35:06', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (3, 157, 'park-auth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n', '756a03974b815ff3febec9744c633cf4', '2025-07-12 11:35:10', '2025-07-12 03:35:10', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (5, 158, 'park-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml', '1efaabbb1af2a55d8484c9232efec24a', '2025-07-12 11:35:16', '2025-07-12 03:35:16', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (8, 159, 'park-file-dev.yml', 'DEFAULT_GROUP', '', '# 本地文件上传    \nfile:\n    domain: http://127.0.0.1:9300\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', '95bb71a3e1301faa5d04c1e6685936cb', '2025-07-12 11:35:19', '2025-07-12 03:35:20', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (10, 160, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n', 'a85b912b55c71764159f7f1487c59c5a', '2025-07-12 11:35:23', '2025-07-12 03:35:24', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (10, 161, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n', 'a85b912b55c71764159f7f1487c59c5a', '2025-07-12 11:35:29', '2025-07-12 03:35:30', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 162, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 微信小程序API配置\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code', '3c17f0ec88455cf95ceddbf2608eda2b', '2025-07-12 11:35:33', '2025-07-12 03:35:34', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (21, 163, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true', 'e5c4a07fc4ddfc06f70c489f3cda79bc', '2025-07-12 11:35:42', '2025-07-12 03:35:42', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (24, 164, 'sizhuo-config.yml', 'DEFAULT_GROUP', '', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n', '84fa2dbf0322c86010606bdddf8580ac', '2025-07-12 11:35:45', '2025-07-12 03:35:45', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (26, 165, 'common-dev.yml', 'DEFAULT_GROUP', '', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false\n\n# 银联支付配置\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# 将来可能的通用配置预留位置', '99b7a30af39c701cfd4f48a9f891237e', '2025-07-12 11:35:49', '2025-07-12 03:35:50', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (26, 166, 'common-dev.yml', 'DEFAULT_GROUP', '', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false\n\n# 银联支付配置\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# 将来可能的通用配置预留位置', '99b7a30af39c701cfd4f48a9f891237e', '2025-07-12 11:35:59', '2025-07-12 03:36:00', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 167, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 微信小程序API配置\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code', '3c17f0ec88455cf95ceddbf2608eda2b', '2025-07-12 11:36:44', '2025-07-12 03:36:45', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 168, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', 'ce47cb40aef5c3ae75f81706b75f06d2', '2025-07-12 11:37:14', '2025-07-12 03:37:15', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (26, 169, 'common-dev.yml', 'DEFAULT_GROUP', '', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false', '9b888a075373e2b0decd67ff85fa7ee7', '2025-07-12 21:03:09', '2025-07-12 13:03:10', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (26, 170, 'common-dev.yml', 'DEFAULT_GROUP', '', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n    org.mybatis: DEBUG\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n  \n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false', 'dcb8f439e77bf3582dd8c1043f229e2c', '2025-07-12 21:42:08', '2025-07-12 13:42:08', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (26, 171, 'common-dev.yml', 'DEFAULT_GROUP', '', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n  \n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false', '2cf96926b52a27aec60b22f55fffdd55', '2025-07-12 22:04:22', '2025-07-12 14:04:22', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 172, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', 'ce47cb40aef5c3ae75f81706b75f06d2', '2025-07-12 22:46:23', '2025-07-12 14:46:23', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 173, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', 'c4a595377b1e81f7be445504858dff7d', '2025-07-14 09:55:53', '2025-07-14 01:55:54', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 174, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://2a7210e2a155.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://2a7210e2a155.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://2a7210e2a155.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '205ee5b30c55d090793cc72b9e6f6a2e', '2025-07-15 11:05:15', '2025-07-15 03:05:15', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 175, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://beff8f70fc84.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://beff8f70fc84.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://beff8f70fc84.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '824a00af7bdae199e02b61f6ae979b6c', '2025-07-17 14:04:09', '2025-07-17 06:04:09', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 176, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://db106fec84ca.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://db106fec84ca.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://db106fec84ca.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '4a9ad62ca9f047a45f3d648cc2be491c', '2025-07-18 09:15:23', '2025-07-18 01:15:24', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 177, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'e6f0cead56e94162e99231d0c3d18d79', '2025-07-20 12:45:24', '2025-07-20 04:45:25', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 178, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: false\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'c49a91c23a60b6a2370d6d154d88e1d4', '2025-07-20 12:45:54', '2025-07-20 04:45:54', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 179, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'e6f0cead56e94162e99231d0c3d18d79', '2025-07-20 18:19:20', '2025-07-20 10:19:21', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 180, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'a81017369da2ceacaca6aecae50b8107', '2025-07-20 18:27:23', '2025-07-20 10:27:24', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 181, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', '06bf2a7a4fc5f6144cb0ac7119d9a346', '2025-07-20 18:29:27', '2025-07-20 10:29:28', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 182, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://5fb7bf9c4ab9.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://5fb7bf9c4ab9.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://5fb7bf9c4ab9.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '70c1bced35f6e25cac1336e296c7358d', '2025-07-21 10:56:13', '2025-07-21 02:56:13', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 183, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://5fb7bf9c4ab9.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://5fb7bf9c4ab9.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://5fb7bf9c4ab9.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '70c1bced35f6e25cac1336e296c7358d', '2025-07-21 12:50:25', '2025-07-21 04:50:26', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (8, 184, 'park-file-dev.yml', 'DEFAULT_GROUP', '', '# 本地文件上传    \nfile:\n    domain: http://127.0.0.1:9300\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', '95bb71a3e1301faa5d04c1e6685936cb', '2025-07-22 13:25:43', '2025-07-22 05:25:44', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (8, 185, 'park-file-dev.yml', 'DEFAULT_GROUP', '', '# 本地文件上传    \nfile:\n    domain: http://**************:9202\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', 'aff3994a74d4df03d047556902205b75', '2025-07-22 13:29:17', '2025-07-22 05:29:18', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (8, 186, 'park-file-dev.yml', 'DEFAULT_GROUP', '', '# 本地文件上传    \nfile:\n    domain: http://**************:9202\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', 'aff3994a74d4df03d047556902205b75', '2025-07-22 13:31:57', '2025-07-22 05:31:57', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 187, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', '06bf2a7a4fc5f6144cb0ac7119d9a346', '2025-07-22 21:00:51', '2025-07-22 13:00:52', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 188, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://bb0f5c66dbc5.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://bb0f5c66dbc5.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://bb0f5c66dbc5.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '919b4cf7519d9889f743b49bfd502270', '2025-07-23 09:22:03', '2025-07-23 01:22:04', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 189, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://836bd6e61729.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://836bd6e61729.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://836bd6e61729.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '7752e7121786b94795f9e1be08e891d2', '2025-07-24 11:46:08', '2025-07-24 03:46:08', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 190, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://a8b4b05af0bb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://a8b4b05af0bb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', 'ce446378fccd7aed0d58bc8a3ab0e19e', '2025-07-24 13:59:01', '2025-07-24 05:59:02', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 191, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'ffddf4d49dc02b25b7aac029af06d965', '2025-07-24 13:59:27', '2025-07-24 05:59:28', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 192, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://a8b4b05af0bb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://a8b4b05af0bb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', 'ce446378fccd7aed0d58bc8a3ab0e19e', '2025-07-24 14:08:47', '2025-07-24 06:08:47', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 193, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://a8b4b05af0bb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    # invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://a8b4b05af0bb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', '96465eb48bca0feebd5ecb83cac4d23e', '2025-07-24 14:09:01', '2025-07-24 06:09:01', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 194, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://a8b4b05af0bb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    # invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://a8b4b05af0bb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', 'e9be7654db31695a93e4c06056d809e4', '2025-07-24 14:19:14', '2025-07-24 06:19:15', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 195, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://a8b4b05af0bb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api\n    # invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://a8b4b05af0bb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', 'f687f3c54f96c2ecfbe417cdda5a6463', '2025-07-24 14:26:57', '2025-07-24 06:26:58', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (8, 196, 'park-file-dev.yml', 'DEFAULT_GROUP', '', '# 本地文件上传    \nfile:\n    domain: http://**************:9202\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', 'aff3994a74d4df03d047556902205b75', '2025-07-24 23:01:42', '2025-07-24 15:01:43', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 197, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://a8b4b05af0bb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://a8b4b05af0bb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    # invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://a8b4b05af0bb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', '6e810454bd1d07377906e4c4dba2610f', '2025-07-25 09:23:07', '2025-07-25 01:23:07', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 198, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', '2e16dbee5afdae0f95509c69c83b42b9', '2025-07-25 12:38:12', '2025-07-25 04:38:13', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 199, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  # captcha:\n  #   enabled: true\n  #   type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'a16b9bd27770575323376d5ad9c25b2f', '2025-07-25 12:38:50', '2025-07-25 04:38:50', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 200, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', '2e16dbee5afdae0f95509c69c83b42b9', '2025-07-25 12:42:36', '2025-07-25 04:42:37', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (2, 201, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  # captcha:\n  #   enabled: true\n  #   type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'a16b9bd27770575323376d5ad9c25b2f', '2025-07-25 12:44:57', '2025-07-25 04:44:58', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 202, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://cfae83b3fad7.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://cfae83b3fad7.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://cfae83b3fad7.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    # invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://cfae83b3fad7.ngrok-free.app/wx/invoiceRecord/invoiceCallback', '155eddb067c669d2a8f99bbedd91287c', '2025-07-25 22:16:42', '2025-07-25 14:16:43', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 203, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://67f1b8a83e2b.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://67f1b8a83e2b.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://67f1b8a83e2b.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    # invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://67f1b8a83e2b.ngrok-free.app/wx/invoiceRecord/invoiceCallback', '4683e459d34df6cc8af571bfcb14e52d', '2025-07-25 23:35:46', '2025-07-25 15:35:46', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (11, 204, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://67f1b8a83e2b.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://67f1b8a83e2b.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://67f1b8a83e2b.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 15\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    # invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://67f1b8a83e2b.ngrok-free.app/wx/invoiceRecord/invoiceCallback', 'ab4e4ed9900284680e2e74f2af3801ea', '2025-07-26 10:23:38', '2025-07-26 02:23:39', NULL, '0:0:0:0:0:0:0:1', 'U', '', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 205, 'park-gateway-test.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统管理\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸控制\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 管理端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,routes,gateway\n  endpoint:\n    health:\n      show-details: always\n\n# 安全配置\nsecurity:\n  # 验证码设置\n  captcha:\n    enabled: true\n    type: math\n  # XSS防护\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /code\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut', 'aba808eb8e16fa4382d17a8c02e5b99b', '2025-07-26 12:56:40', '2025-07-26 04:56:41', '', '0:0:0:0:0:0:0:1', 'I', 'db222c85-606a-489b-95ac-ac568029b9b2', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 206, 'park-auth-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      web-stat-filter:\n        enabled: true\n      stat-view-servlet:\n        enabled: true\n        allow:\n        url-pattern: /druid/*\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '36db0103978f68a849709484eac784cd', '2025-07-26 12:56:40', '2025-07-26 04:56:41', '', '0:0:0:0:0:0:0:1', 'I', 'db222c85-606a-489b-95ac-ac568029b9b2', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 207, 'park-system-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        allow:\n        url-pattern: /druid/*\n        # 监控页面登录用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.system\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '3076468f95d4f8efebd042c7d3ba84cc', '2025-07-26 12:56:40', '2025-07-26 04:56:41', '', '0:0:0:0:0:0:0:1', 'I', 'db222c85-606a-489b-95ac-ac568029b9b2', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 208, 'park-file-test.yml', 'DEFAULT_GROUP', '', '# 本地文件上传配置 - Docker容器路径\nfile:\n    domain: http://127.0.0.1:9300\n    path: /app/upload\n    prefix: /statics\n\n# FastDFS分布式文件系统配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio对象存储配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      # 单个文件最大大小\n      max-file-size: 10MB\n      # 总请求最大大小\n      max-request-size: 20MB\n      # 启用文件上传\n      enabled: true\n      # 大文件延迟解析\n      resolve-lazily: false\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '5e8ec771f6899169751d53e972d0d047', '2025-07-26 12:56:40', '2025-07-26 04:56:41', '', '0:0:0:0:0:0:0:1', 'I', 'db222c85-606a-489b-95ac-ac568029b9b2', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 209, 'sizhuo-config-test.yml', 'DEFAULT_GROUP', '', '# 思卓品牌道闸配置\nsizhuo:\n  # 品牌标识\n  brandId: sizhuo\n  # 品牌名称\n  brandName: 思卓\n  # 道闸API请求地址\n  requestUrl: http://qr.it-wy.cn:83\n  # 停车场区域配置\n  areas:\n    - # 物理停车场ID\n      physicalId: 20240926133058945          \n      # 逻辑停车场ID\n      logicalId: 728424271419936768               \n      # 停车场名称\n      name: 江山路\n      # 通道规则配置\n      channelRules:\n        # 入场不收费通道列表（通道3免费入场）\n        entryDisabledChannels: [\"3\"]  \n        # 出场不收费通道列表（通道4免费出场）\n        exitDisabledChannels: [\"4\"]', 'bd6c04ae7612a8f600ca82944277d57a', '2025-07-26 12:56:40', '2025-07-26 04:56:41', '', '0:0:0:0:0:0:0:1', 'I', 'db222c85-606a-489b-95ac-ac568029b9b2', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 210, 'park-wx-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.wx\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    # 开启驼峰命名转换\n    map-underscore-to-camel-case: true\n\n# 小程序API配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grant-type: authorization_code\n\n# 银联支付API配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  app-id: wxdcd31ee3e79190cc\n  app-key: 2e18a85cfa56e071fa00e96aaa9012cc\n  # 订单支付回调地址\n  notify-url-order: https://test-parking.example.com/wx/parking/order/payCallback\n  # VIP会员支付回调地址\n  notify-url-vip: https://test-parking.example.com/wx/package/payCallback\n  # 支付宝订单回调地址\n  notify-url-order-alipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\n  # 请求超时时间（秒）\n  timeout: 5\n  # 发票配置\n  invoice-url: https://fapiao.chinaums.com/fapiao-api/\n  invoice-msg-src: LINGANG_JT\n  invoice-key: 12cd2d10f2204107bf87df57aa7a4c4e\n  # 发票回调地址\n  notify-url-order-invoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', 'e9f0a12c4f85b5d72a05fb3da5e999c6', '2025-07-26 12:56:40', '2025-07-26 04:56:41', '', '0:0:0:0:0:0:0:1', 'I', 'db222c85-606a-489b-95ac-ac568029b9b2', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 211, 'park-wxauth-test.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n    \n# 短信服务配置\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: test-api-user\n  api-key: ************************************\n  sign: 【捷运停车充电】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 微信小程序API配置\nwx-api:\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '810183f69311ef7af4b226518de7d807', '2025-07-26 12:56:40', '2025-07-26 04:56:41', '', '0:0:0:0:0:0:0:1', 'I', 'db222c85-606a-489b-95ac-ac568029b9b2', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 212, 'park-gate-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.gate\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    # 开启驼峰命名转换\n    map-underscore-to-camel-case: true\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '77af2fd1c3c7fe99666d2f926643b291', '2025-07-26 12:56:40', '2025-07-26 04:56:41', '', '0:0:0:0:0:0:0:1', 'I', 'db222c85-606a-489b-95ac-ac568029b9b2', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 213, 'common-test.yml', 'DEFAULT_GROUP', '', '# 测试环境通用配置\r\n# 所有微服务共享配置\r\n\r\n# 日志配置\r\nlogging:\r\n  level:\r\n    com.lgjy: INFO\r\n  file:\r\n    name: logs/${spring.application.name}/app.log\r\n    max-size: 100MB\r\n    max-history: 30\r\n\r\n# Sentinel配置\r\nspring:\r\n  cloud:\r\n    sentinel:\r\n      log:\r\n        dir: logs/sentinel\r\n        switch-pid: false\r\n\r\n# 管理端点配置\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: health,info,metrics\r\n  endpoint:\r\n    health:\r\n      show-details: always', 'f3a501aa32864dfc2fbe9ed2e203aff0', '2025-07-26 12:56:40', '2025-07-26 04:56:41', '', '0:0:0:0:0:0:0:1', 'I', 'db222c85-606a-489b-95ac-ac568029b9b2', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 214, 'park-gateway-test.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统管理\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸控制\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 管理端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,routes,gateway\n  endpoint:\n    health:\n      show-details: always\n\n# 安全配置\nsecurity:\n  # 验证码设置\n  captcha:\n    enabled: true\n    type: math\n  # XSS防护\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /code\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut', 'aba808eb8e16fa4382d17a8c02e5b99b', '2025-07-26 12:57:40', '2025-07-26 04:57:41', '', '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 215, 'park-auth-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      web-stat-filter:\n        enabled: true\n      stat-view-servlet:\n        enabled: true\n        allow:\n        url-pattern: /druid/*\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '36db0103978f68a849709484eac784cd', '2025-07-26 12:57:40', '2025-07-26 04:57:41', '', '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 216, 'park-system-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        allow:\n        url-pattern: /druid/*\n        # 监控页面登录用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.system\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '3076468f95d4f8efebd042c7d3ba84cc', '2025-07-26 12:57:40', '2025-07-26 04:57:41', '', '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 217, 'park-file-test.yml', 'DEFAULT_GROUP', '', '# 本地文件上传配置 - Docker容器路径\nfile:\n    domain: http://127.0.0.1:9300\n    path: /app/upload\n    prefix: /statics\n\n# FastDFS分布式文件系统配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio对象存储配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      # 单个文件最大大小\n      max-file-size: 10MB\n      # 总请求最大大小\n      max-request-size: 20MB\n      # 启用文件上传\n      enabled: true\n      # 大文件延迟解析\n      resolve-lazily: false\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '5e8ec771f6899169751d53e972d0d047', '2025-07-26 12:57:40', '2025-07-26 04:57:41', '', '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 218, 'sizhuo-config-test.yml', 'DEFAULT_GROUP', '', '# 思卓品牌道闸配置\nsizhuo:\n  # 品牌标识\n  brandId: sizhuo\n  # 品牌名称\n  brandName: 思卓\n  # 道闸API请求地址\n  requestUrl: http://qr.it-wy.cn:83\n  # 停车场区域配置\n  areas:\n    - # 物理停车场ID\n      physicalId: 20240926133058945          \n      # 逻辑停车场ID\n      logicalId: 728424271419936768               \n      # 停车场名称\n      name: 江山路\n      # 通道规则配置\n      channelRules:\n        # 入场不收费通道列表（通道3免费入场）\n        entryDisabledChannels: [\"3\"]  \n        # 出场不收费通道列表（通道4免费出场）\n        exitDisabledChannels: [\"4\"]', 'bd6c04ae7612a8f600ca82944277d57a', '2025-07-26 12:57:40', '2025-07-26 04:57:41', '', '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 219, 'park-wx-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.wx\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    # 开启驼峰命名转换\n    map-underscore-to-camel-case: true\n\n# 小程序API配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grant-type: authorization_code\n\n# 银联支付API配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  app-id: wxdcd31ee3e79190cc\n  app-key: 2e18a85cfa56e071fa00e96aaa9012cc\n  # 订单支付回调地址\n  notify-url-order: https://test-parking.example.com/wx/parking/order/payCallback\n  # VIP会员支付回调地址\n  notify-url-vip: https://test-parking.example.com/wx/package/payCallback\n  # 支付宝订单回调地址\n  notify-url-order-alipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\n  # 请求超时时间（秒）\n  timeout: 5\n  # 发票配置\n  invoice-url: https://fapiao.chinaums.com/fapiao-api/\n  invoice-msg-src: LINGANG_JT\n  invoice-key: 12cd2d10f2204107bf87df57aa7a4c4e\n  # 发票回调地址\n  notify-url-order-invoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', 'e9f0a12c4f85b5d72a05fb3da5e999c6', '2025-07-26 12:57:40', '2025-07-26 04:57:41', '', '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 220, 'park-wxauth-test.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n    \n# 短信服务配置\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: test-api-user\n  api-key: ************************************\n  sign: 【捷运停车充电】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 微信小程序API配置\nwx-api:\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '810183f69311ef7af4b226518de7d807', '2025-07-26 12:57:40', '2025-07-26 04:57:41', '', '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 221, 'park-gate-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.gate\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    # 开启驼峰命名转换\n    map-underscore-to-camel-case: true\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '77af2fd1c3c7fe99666d2f926643b291', '2025-07-26 12:57:40', '2025-07-26 04:57:41', '', '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 222, 'common-test.yml', 'DEFAULT_GROUP', '', '# 测试环境通用配置\r\n# 所有微服务共享配置\r\n\r\n# 日志配置\r\nlogging:\r\n  level:\r\n    com.lgjy: INFO\r\n  file:\r\n    name: logs/${spring.application.name}/app.log\r\n    max-size: 100MB\r\n    max-history: 30\r\n\r\n# Sentinel配置\r\nspring:\r\n  cloud:\r\n    sentinel:\r\n      log:\r\n        dir: logs/sentinel\r\n        switch-pid: false\r\n\r\n# 管理端点配置\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: health,info,metrics\r\n  endpoint:\r\n    health:\r\n      show-details: always', 'f3a501aa32864dfc2fbe9ed2e203aff0', '2025-07-26 12:57:41', '2025-07-26 04:57:41', '', '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (36, 223, 'park-gateway-test.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统管理\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸控制\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 管理端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,routes,gateway\n  endpoint:\n    health:\n      show-details: always\n\n# 安全配置\nsecurity:\n  # 验证码设置\n  captcha:\n    enabled: true\n    type: math\n  # XSS防护\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /code\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut', 'aba808eb8e16fa4382d17a8c02e5b99b', '2025-07-26 13:07:07', '2025-07-26 05:07:08', NULL, '0:0:0:0:0:0:0:1', 'D', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (37, 224, 'park-auth-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      web-stat-filter:\n        enabled: true\n      stat-view-servlet:\n        enabled: true\n        allow:\n        url-pattern: /druid/*\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '36db0103978f68a849709484eac784cd', '2025-07-26 13:07:07', '2025-07-26 05:07:08', NULL, '0:0:0:0:0:0:0:1', 'D', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (38, 225, 'park-system-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        allow:\n        url-pattern: /druid/*\n        # 监控页面登录用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.system\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '3076468f95d4f8efebd042c7d3ba84cc', '2025-07-26 13:07:07', '2025-07-26 05:07:08', NULL, '0:0:0:0:0:0:0:1', 'D', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (39, 226, 'park-file-test.yml', 'DEFAULT_GROUP', '', '# 本地文件上传配置 - Docker容器路径\nfile:\n    domain: http://127.0.0.1:9300\n    path: /app/upload\n    prefix: /statics\n\n# FastDFS分布式文件系统配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio对象存储配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      # 单个文件最大大小\n      max-file-size: 10MB\n      # 总请求最大大小\n      max-request-size: 20MB\n      # 启用文件上传\n      enabled: true\n      # 大文件延迟解析\n      resolve-lazily: false\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '5e8ec771f6899169751d53e972d0d047', '2025-07-26 13:07:07', '2025-07-26 05:07:08', NULL, '0:0:0:0:0:0:0:1', 'D', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (40, 227, 'sizhuo-config-test.yml', 'DEFAULT_GROUP', '', '# 思卓品牌道闸配置\nsizhuo:\n  # 品牌标识\n  brandId: sizhuo\n  # 品牌名称\n  brandName: 思卓\n  # 道闸API请求地址\n  requestUrl: http://qr.it-wy.cn:83\n  # 停车场区域配置\n  areas:\n    - # 物理停车场ID\n      physicalId: 20240926133058945          \n      # 逻辑停车场ID\n      logicalId: 728424271419936768               \n      # 停车场名称\n      name: 江山路\n      # 通道规则配置\n      channelRules:\n        # 入场不收费通道列表（通道3免费入场）\n        entryDisabledChannels: [\"3\"]  \n        # 出场不收费通道列表（通道4免费出场）\n        exitDisabledChannels: [\"4\"]', 'bd6c04ae7612a8f600ca82944277d57a', '2025-07-26 13:07:07', '2025-07-26 05:07:08', NULL, '0:0:0:0:0:0:0:1', 'D', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (41, 228, 'park-wx-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.wx\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    # 开启驼峰命名转换\n    map-underscore-to-camel-case: true\n\n# 小程序API配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grant-type: authorization_code\n\n# 银联支付API配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  app-id: wxdcd31ee3e79190cc\n  app-key: 2e18a85cfa56e071fa00e96aaa9012cc\n  # 订单支付回调地址\n  notify-url-order: https://test-parking.example.com/wx/parking/order/payCallback\n  # VIP会员支付回调地址\n  notify-url-vip: https://test-parking.example.com/wx/package/payCallback\n  # 支付宝订单回调地址\n  notify-url-order-alipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\n  # 请求超时时间（秒）\n  timeout: 5\n  # 发票配置\n  invoice-url: https://fapiao.chinaums.com/fapiao-api/\n  invoice-msg-src: LINGANG_JT\n  invoice-key: 12cd2d10f2204107bf87df57aa7a4c4e\n  # 发票回调地址\n  notify-url-order-invoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', 'e9f0a12c4f85b5d72a05fb3da5e999c6', '2025-07-26 13:07:07', '2025-07-26 05:07:08', NULL, '0:0:0:0:0:0:0:1', 'D', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (42, 229, 'park-wxauth-test.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n    \n# 短信服务配置\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: test-api-user\n  api-key: ************************************\n  sign: 【捷运停车充电】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 微信小程序API配置\nwx-api:\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '810183f69311ef7af4b226518de7d807', '2025-07-26 13:07:07', '2025-07-26 05:07:08', NULL, '0:0:0:0:0:0:0:1', 'D', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (43, 230, 'park-gate-test.yml', 'DEFAULT_GROUP', '', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接池大小\n      initial-size: 5\n      # 最小空闲连接数\n      min-idle: 10\n      # 最大活跃连接数\n      max-active: 20\n      # 连接等待超时时间\n      max-wait: 60000\n      # 连接超时时间\n      connect-timeout: 30000\n      # Socket超时时间\n      socket-timeout: 60000\n      # 空闲连接检查间隔\n      time-between-eviction-runs-millis: 60000\n      # 最小空闲时间\n      min-evictable-idle-time-millis: 300000\n      # 最大空闲时间\n      max-evictable-idle-time-millis: 900000\n      # 验证查询\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      # Web统计过滤器\n      web-stat-filter:\n        enabled: true\n      # 统计视图Servlet\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            # 允许多语句\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 类型别名包路径\n  type-aliases-package: com.lgjy.gate\n  # Mapper XML文件位置\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    # 开启驼峰命名转换\n    map-underscore-to-camel-case: true\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '77af2fd1c3c7fe99666d2f926643b291', '2025-07-26 13:07:07', '2025-07-26 05:07:08', NULL, '0:0:0:0:0:0:0:1', 'D', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (44, 231, 'common-test.yml', 'DEFAULT_GROUP', '', '# 测试环境通用配置\r\n# 所有微服务共享配置\r\n\r\n# 日志配置\r\nlogging:\r\n  level:\r\n    com.lgjy: INFO\r\n  file:\r\n    name: logs/${spring.application.name}/app.log\r\n    max-size: 100MB\r\n    max-history: 30\r\n\r\n# Sentinel配置\r\nspring:\r\n  cloud:\r\n    sentinel:\r\n      log:\r\n        dir: logs/sentinel\r\n        switch-pid: false\r\n\r\n# 管理端点配置\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: health,info,metrics\r\n  endpoint:\r\n    health:\r\n      show-details: always', 'f3a501aa32864dfc2fbe9ed2e203aff0', '2025-07-26 13:07:07', '2025-07-26 05:07:08', NULL, '0:0:0:0:0:0:0:1', 'D', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 232, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', '2e16dbee5afdae0f95509c69c83b42b9', '2025-07-26 13:07:31', '2025-07-26 05:07:31', NULL, '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 233, 'park-auth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n', '756a03974b815ff3febec9744c633cf4', '2025-07-26 13:07:31', '2025-07-26 05:07:31', NULL, '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 234, 'park-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml', '1efaabbb1af2a55d8484c9232efec24a', '2025-07-26 13:07:31', '2025-07-26 05:07:31', NULL, '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 235, 'park-file-dev.yml', 'DEFAULT_GROUP', '', '# 本地文件上传    \nfile:\n    domain: http://**************:9202\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', '0c541f89595bd67bc4824b36c24f527e', '2025-07-26 13:07:31', '2025-07-26 05:07:31', NULL, '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 236, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n', 'a85b912b55c71764159f7f1487c59c5a', '2025-07-26 13:07:31', '2025-07-26 05:07:31', NULL, '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 237, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://4924d67a2cbb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://4924d67a2cbb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://4924d67a2cbb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 15\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    # invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://4924d67a2cbb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', 'eca7ee68fbbbc47cd0d41d84e47fdc5d', '2025-07-26 13:07:31', '2025-07-26 05:07:31', NULL, '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 238, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true', 'e5c4a07fc4ddfc06f70c489f3cda79bc', '2025-07-26 13:07:31', '2025-07-26 05:07:31', NULL, '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 239, 'sizhuo-config.yml', 'DEFAULT_GROUP', '', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n', '84fa2dbf0322c86010606bdddf8580ac', '2025-07-26 13:07:31', '2025-07-26 05:07:31', NULL, '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (0, 240, 'common-dev.yml', 'DEFAULT_GROUP', '', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n  \n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false', '2cf96926b52a27aec60b22f55fffdd55', '2025-07-26 13:07:31', '2025-07-26 05:07:31', NULL, '0:0:0:0:0:0:0:1', 'I', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (45, 241, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', '2e16dbee5afdae0f95509c69c83b42b9', '2025-07-26 13:10:36', '2025-07-26 05:10:37', NULL, '0:0:0:0:0:0:0:1', 'U', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (46, 242, 'park-auth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n', '756a03974b815ff3febec9744c633cf4', '2025-07-26 13:14:08', '2025-07-26 05:14:09', NULL, '0:0:0:0:0:0:0:1', 'U', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (45, 243, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n\n# 管理端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,routes,gateway\n  endpoint:\n    health:\n      show-details: always\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', '7f7e3cabeab206ffb1722aee9b466398', '2025-07-26 13:19:15', '2025-07-26 05:19:15', NULL, '0:0:0:0:0:0:0:1', 'U', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (47, 244, 'park-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml', '1efaabbb1af2a55d8484c9232efec24a', '2025-07-26 13:21:00', '2025-07-26 05:21:00', NULL, '0:0:0:0:0:0:0:1', 'U', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (49, 245, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n', 'a85b912b55c71764159f7f1487c59c5a', '2025-07-26 13:21:49', '2025-07-26 05:21:50', NULL, '0:0:0:0:0:0:0:1', 'U', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (50, 246, 'park-wx-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://4924d67a2cbb.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://4924d67a2cbb.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://4924d67a2cbb.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 15\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    # invoiceUrl: https://api-mop.chinaums.com/v1/invoice/invoice\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://4924d67a2cbb.ngrok-free.app/wx/invoiceRecord/invoiceCallback', 'eca7ee68fbbbc47cd0d41d84e47fdc5d', '2025-07-26 13:22:23', '2025-07-26 05:22:23', NULL, '0:0:0:0:0:0:0:1', 'U', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (51, 247, 'park-gate-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true', 'e5c4a07fc4ddfc06f70c489f3cda79bc', '2025-07-26 13:22:54', '2025-07-26 05:22:54', NULL, '0:0:0:0:0:0:0:1', 'U', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (45, 248, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'dd356040fba2edd284003e0cce48f9f5', '2025-07-26 13:32:23', '2025-07-26 05:32:23', NULL, '0:0:0:0:0:0:0:1', 'U', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);
INSERT INTO `his_config_info` VALUES (45, 249, 'park-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/warehouse/*\n      - /wx/package/payCallback\n      - /wx/agreement/getByType\n      - /wx/invoiceRecord/invoiceCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n', 'dd356040fba2edd284003e0cce48f9f5', '2025-07-26 13:50:02', '2025-07-26 05:50:03', NULL, '0:0:0:0:0:0:0:1', 'U', '15456384-8b5a-416c-b72a-053ae7042cf9', '', 'formal', NULL, NULL);

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'role',
  `resource` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'resource',
  `action` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'action',
  UNIQUE INDEX `uk_role_permission`(`role`, `resource`, `action`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of permissions
-- ----------------------------

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'username',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'role',
  UNIQUE INDEX `idx_user_role`(`username`, `role`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES ('nacos', 'ROLE_ADMIN');

-- ----------------------------
-- Table structure for tenant_capacity
-- ----------------------------
DROP TABLE IF EXISTS `tenant_capacity`;
CREATE TABLE `tenant_capacity`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Tenant ID',
  `quota` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数',
  `max_aggr_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '租户容量信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_capacity
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_info`;
CREATE TABLE `tenant_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kp` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'kp',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tenant_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT 'tenant_name',
  `tenant_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'tenant_desc',
  `create_source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'create_source',
  `gmt_create` bigint NOT NULL COMMENT '创建时间',
  `gmt_modified` bigint NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_info_kptenantid`(`kp`, `tenant_id`) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'tenant_info' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_info
-- ----------------------------
INSERT INTO `tenant_info` VALUES (2, '1', 'db222c85-606a-489b-95ac-ac568029b9b2', 'test', '测试', 'nacos', 1753505766772, 1753505766772);
INSERT INTO `tenant_info` VALUES (3, '1', '15456384-8b5a-416c-b72a-053ae7042cf9', 'dev', '开发', 'nacos', 1753505843819, 1753505843819);

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'username',
  `password` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'password',
  `enabled` tinyint(1) NOT NULL COMMENT 'enabled',
  PRIMARY KEY (`username`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES ('nacos', '$2a$10$0p86qFLFpr5u0T7qHLJDROFzcveA/0hxLEfn3goz4VIGcbuqlOkU2', 1);

SET FOREIGN_KEY_CHECKS = 1;
